<template>
  <div class="dialog-overlay">
    <div class="dialog-container">
      <div class="dialog-header">
        <div class="dialog-title">选择头像</div>
        <div class="dialog-close" @click="$emit('close')">
          <DeleteIcon :size="24" color="var(--primary-color)" />
        </div>
      </div>
      <div class="dialog-content">
        <!-- 默认头像选择区域 -->
        <div class="avatar-section">
          <!-- 头像网格 -->
          <div class="avatar-grid">
            <div
              v-for="(avatar, index) in allAvatars"
              :key="avatar.id"
              class="avatar-option"
              @click="selectAvatar(avatar.id)"
            >
              <img :src="avatar.src" :alt="`默认头像${index + 1}`" class="avatar-preview" />
            </div>
          </div>
        </div>

        <!-- 上传头像区域 -->
        <div class="upload-section">
          <div class="section-title">上传自定义头像</div>
          <div class="upload-button" @click="$emit('upload-avatar')">
            <i class="iconfont icon-camera upload-icon"></i>
            <span class="upload-text">点击上传头像</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getDefaultAvatars } from '@/utils/avatarUtils';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';

// Emits定义
const emit = defineEmits<{
  close: [];
  'select-avatar': [avatarUrl: string];
  'upload-avatar': [];
}>();

// 获取所有默认头像
const allAvatars = getDefaultAvatars();

// 选择头像
const selectAvatar = (avatarId: string) => {
  emit('select-avatar', avatarId);
};
</script>

<style lang="scss" scoped>
// 对话框样式
.dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 5vh;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 650px;
  max-height: 90vh;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.avatar-section,
.upload-section {
  border: 2px solid var(--border-accent);
  border-radius: 16px;
  padding: 24px;
  background: var(--primary-color-light);
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
}

.section-title {
  color: var(--accent-color);
  font-size: 32px; // 增加8px (原来24px)
  font-weight: 600;
  margin-bottom: 20px;
}

// 头像网格样式
.avatar-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(6, 1fr);
  gap: 16px;
  padding: 16px;
  min-height: 600px;
  max-height: 70vh;
  overflow-y: auto;
}

.avatar-option {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  justify-self: center;

  &:hover {
    border-color: var(--accent-color);
    transform: scale(1.1);
    box-shadow: var(--shadow-accent);
  }
}



.avatar-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.upload-section {
  text-align: center;
}

.upload-button {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 24px 32px;
  background: var(--primary-color-light);
  border: 2px solid var(--primary-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--primary-color);


}

.upload-icon {
  font-size: 40px; // 增加8px (原来32px)
}

.upload-text {
  font-size: 28px; // 增加8px (原来20px)
  font-weight: 600;
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
